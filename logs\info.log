[2025-06-27T03:40:53.700Z] [INFO] 创建数据库目录 {"path":"./database"}
[2025-06-27T03:40:59.610Z] [INFO] 默认配置插入完成
[2025-06-27T03:41:00.356Z] [INFO] 默认管理员创建完成 {"username":"admin"}
[2025-06-27T03:41:00.358Z] [INFO] 数据库初始化完成 {"path":"./database/shop.db"}
[2025-06-27T03:43:35.110Z] [INFO] 数据库连接成功 {"path":"./database/shop.db"}
[2025-06-27T03:43:35.306Z] [INFO] 创建分类 {"name":"游戏充值"}
[2025-06-27T03:43:35.522Z] [INFO] 创建分类 {"name":"软件激活"}
[2025-06-27T03:43:35.720Z] [INFO] 创建分类 {"name":"会员服务"}
[2025-06-27T03:43:35.999Z] [INFO] 创建分类 {"name":"礼品卡"}
[2025-06-27T03:43:36.124Z] [INFO] 创建商品 {"name":"王者荣耀点券","id":1}
[2025-06-27T03:43:36.689Z] [INFO] 创建商品 {"name":"和平精英UC","id":2}
[2025-06-27T03:43:37.029Z] [INFO] 创建商品 {"name":"原神创世结晶","id":3}
[2025-06-27T03:43:37.380Z] [INFO] 创建商品 {"name":"Windows 11 Pro","id":4}
[2025-06-27T03:43:37.593Z] [INFO] 创建商品 {"name":"Office 2021","id":5}
[2025-06-27T03:43:37.836Z] [INFO] 创建商品 {"name":"爱奇艺VIP月卡","id":6}
[2025-06-27T03:43:38.104Z] [INFO] 创建商品 {"name":"腾讯视频VIP月卡","id":7}
[2025-06-27T03:43:40.190Z] [INFO] 为商品创建卡密 {"product":"王者荣耀点券","count":10}
[2025-06-27T03:43:41.396Z] [INFO] 为商品创建卡密 {"product":"和平精英UC","count":7}
[2025-06-27T03:43:43.687Z] [INFO] 为商品创建卡密 {"product":"原神创世结晶","count":10}
[2025-06-27T03:43:44.952Z] [INFO] 为商品创建卡密 {"product":"Windows 11 Pro","count":6}
[2025-06-27T03:43:45.880Z] [INFO] 为商品创建卡密 {"product":"Office 2021","count":6}
[2025-06-27T03:43:47.000Z] [INFO] 为商品创建卡密 {"product":"爱奇艺VIP月卡","count":10}
[2025-06-27T03:43:47.854Z] [INFO] 为商品创建卡密 {"product":"腾讯视频VIP月卡","count":8}
[2025-06-27T03:43:47.856Z] [INFO] 数据库种子数据创建完成
[2025-06-27T03:43:47.858Z] [INFO] 数据库连接已关闭
[2025-06-27T03:44:09.100Z] [INFO] 数据库连接成功 {"path":"./database/shop.db"}
[2025-06-27T03:44:09.720Z] [INFO] 创建分类 {"name":"游戏充值"}
[2025-06-27T03:44:10.719Z] [INFO] 创建分类 {"name":"软件激活"}
[2025-06-27T03:44:11.195Z] [INFO] 创建分类 {"name":"会员服务"}
[2025-06-27T03:44:12.208Z] [INFO] 创建分类 {"name":"礼品卡"}
[2025-06-27T03:44:13.152Z] [INFO] 创建商品 {"name":"王者荣耀点券","id":8}
[2025-06-27T03:44:14.352Z] [INFO] 创建商品 {"name":"和平精英UC","id":9}
[2025-06-27T03:44:14.640Z] [INFO] 创建商品 {"name":"原神创世结晶","id":10}
[2025-06-27T03:44:14.972Z] [INFO] 创建商品 {"name":"Windows 11 Pro","id":11}
[2025-06-27T03:44:15.291Z] [INFO] 创建商品 {"name":"Office 2021","id":12}
[2025-06-27T03:44:15.690Z] [INFO] 创建商品 {"name":"爱奇艺VIP月卡","id":13}
[2025-06-27T03:44:15.940Z] [INFO] 创建商品 {"name":"腾讯视频VIP月卡","id":14}
[2025-06-27T03:44:18.406Z] [INFO] 为商品创建卡密 {"product":"王者荣耀点券","count":11}
[2025-06-27T03:44:19.972Z] [INFO] 为商品创建卡密 {"product":"和平精英UC","count":8}
[2025-06-27T03:44:22.275Z] [INFO] 为商品创建卡密 {"product":"原神创世结晶","count":9}
[2025-06-27T03:44:23.773Z] [INFO] 为商品创建卡密 {"product":"Windows 11 Pro","count":7}
[2025-06-27T03:44:26.623Z] [INFO] 为商品创建卡密 {"product":"Office 2021","count":12}
[2025-06-27T03:44:28.806Z] [INFO] 为商品创建卡密 {"product":"爱奇艺VIP月卡","count":9}
[2025-06-27T03:44:32.324Z] [INFO] 为商品创建卡密 {"product":"腾讯视频VIP月卡","count":12}
[2025-06-27T03:44:34.758Z] [INFO] 为商品创建卡密 {"product":"王者荣耀点券","count":10}
[2025-06-27T03:44:39.724Z] [INFO] 为商品创建卡密 {"product":"和平精英UC","count":14}
[2025-06-27T03:44:41.773Z] [INFO] 为商品创建卡密 {"product":"原神创世结晶","count":5}
[2025-06-27T03:44:45.267Z] [INFO] 为商品创建卡密 {"product":"Windows 11 Pro","count":10}
[2025-06-27T03:44:46.556Z] [INFO] 为商品创建卡密 {"product":"Office 2021","count":9}
[2025-06-27T03:44:49.174Z] [INFO] 为商品创建卡密 {"product":"爱奇艺VIP月卡","count":13}
[2025-06-27T03:44:51.872Z] [INFO] 为商品创建卡密 {"product":"腾讯视频VIP月卡","count":7}
[2025-06-27T03:44:52.021Z] [INFO] 数据库种子数据创建完成
[2025-06-27T03:44:52.045Z] [INFO] 数据库连接已关闭
[2025-06-27T03:52:24.257Z] [INFO] 数据库初始化完成
[2025-06-27T03:52:24.555Z] [INFO] 数据库连接成功 {"path":"./database/shop.db"}
[2025-06-27T03:53:40.106Z] [INFO] 数据库初始化完成
[2025-06-27T03:53:40.499Z] [INFO] 数据库连接成功 {"path":"./database/shop.db"}
[2025-06-27T03:54:02.275Z] [INFO] 服务器启动成功，端口: 3000
[2025-06-27T03:54:02.371Z] [INFO] 管理后台: http://localhost:3000/admin
[2025-06-27T03:54:02.482Z] [INFO] API文档: http://localhost:3000/api/docs
[2025-06-27T03:54:36.409Z] [INFO] GET /api/docs {"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
[2025-06-27T03:54:36.826Z] [INFO] API请求 {"method":"GET","url":"/docs","status":200,"duration":"15ms","ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
[2025-06-27T03:54:42.794Z] [INFO] GET /favicon.ico {"ip":"::1","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
[2025-06-27T03:55:17.417Z] [INFO] GET /api/health {"ip":"::ffff:127.0.0.1","userAgent":"curl/8.0.1"}
[2025-06-27T03:55:17.435Z] [INFO] API请求 {"method":"GET","url":"/health","status":200,"duration":"2ms","ip":"::ffff:127.0.0.1","userAgent":"curl/8.0.1"}
[2025-06-27T03:55:41.465Z] [INFO] GET /api/products {"ip":"::ffff:127.0.0.1","userAgent":"curl/8.0.1"}
[2025-06-27T03:55:41.761Z] [INFO] API请求 {"method":"GET","url":"/products","status":200,"duration":"290ms","ip":"::ffff:127.0.0.1","userAgent":"curl/8.0.1"}
[2025-06-27T03:57:25.072Z] [INFO] GET /api/cards?limit=5 {"ip":"::ffff:127.0.0.1","userAgent":"curl/8.0.1"}
[2025-06-27T03:57:26.397Z] [INFO] API请求 {"method":"GET","url":"/cards?limit=5","status":200,"duration":"1323ms","ip":"::ffff:127.0.0.1","userAgent":"curl/8.0.1"}
