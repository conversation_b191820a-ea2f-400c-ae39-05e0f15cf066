{"name": "telegram-card-shop", "version": "1.0.0", "description": "Telegram Bot卡密销售系统", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "db:init": "node src/database/init.js", "db:seed": "node src/database/seed.js"}, "keywords": ["telegram", "bot", "card", "shop", "payment"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "csv-parser": "^3.2.0", "dotenv": "^16.6.0", "ejs": "^3.1.9", "express": "^4.21.2", "express-session": "^1.17.3", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "sqlite3": "^5.1.7", "telegraf": "^4.16.3", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}